{"name": "new_rewardgenixfrontend", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "lint": "eslint .", "preview": "vite preview --port 3001"}, "dependencies": {"@reduxjs/toolkit": "^2.6.0", "axios": "^1.7.9", "crypto-js": "^4.2.0", "jwt-decode": "^4.0.0", "qrcode.react": "^4.2.0", "react": "^19.0.0", "react-bootstrap": "^2.10.9", "react-dom": "^19.0.0", "react-icons": "^5.5.0", "react-redux": "^9.2.0", "react-router-dom": "^7.2.0", "react-toastify": "^11.0.5", "redux": "^5.0.1", "redux-thunk": "^3.1.0"}, "devDependencies": {"@eslint/js": "^9.19.0", "@types/react": "^19.0.8", "@types/react-dom": "^19.0.3", "@vitejs/plugin-react": "^4.3.4", "eslint": "^9.19.0", "eslint-plugin-react": "^7.37.4", "eslint-plugin-react-hooks": "^5.0.0", "eslint-plugin-react-refresh": "^0.4.18", "globals": "^15.14.0", "vite": "^6.1.0"}}