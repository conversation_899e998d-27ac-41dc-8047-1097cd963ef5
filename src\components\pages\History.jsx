import React, { useEffect, useState } from 'react'
import OrderDetails from './OrderDetails'
import api from '../../Api';


export default function History({handleHistoryModalToggle,rt}) {
    const [data,setData]= useState([])

    useEffect(() => {
       
        
        console.log(rt)
        const fetchData = async () => {
          try {
            const response = await api.get("/api/vouchers/order/history");
            console.log(response.data)
            if (response.status === 200) {
             
              setData(response.data.resData);
              
            }
          } catch (error) {
            console.log(error);
          }
        };
        
    
        fetchData();
      }, []);
  return (
   
  
   <>
   
   {
    data && <OrderDetails resData={data} closeOrderModal={handleHistoryModalToggle} odp={false} data={rt.user} />
   }
   </>
   
  )
}
