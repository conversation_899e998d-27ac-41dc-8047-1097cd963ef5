button,
input,
select {
  outline: 0;
}

.btn,
button,
select {
  cursor: pointer;
}

a {
  text-decoration: none;
  color: #777;
}

a:hover {
  text-decoration: none;
  color: #d52b1e;
}

.small {
  font-size: 12px;
}

.mt0 {
  margin-top: 0;
}

.mt4 {
  margin-top: 40px;
}

.mb0 {
  margin-bottom: 0;
}

.actionbg,
.btn,
button {
  border-radius: 30px;
  -webkit-border-radius: 30px;
  -moz-border-radius: 30px;
  -o-border-radius: 30px;
  -ms-border-radius: 30px;
}

.btn:hover,
button:hover {
  transition: 0.5s ease;
  -webkit-transition: 0.5s ease;
  -o-transition: 0.5s ease;
  -ms-transition: 0.5s ease;
  -moz-transition: 0.5s ease;
  background: #d66159;
  color: #fff;
}

.btn {
  background: #555;
  color: #fff;
  text-decoration: none;
  padding: 0 30px;
  text-align: center;
  font-size: 14px;
  display: inline-block;
  height: 38px;
  line-height: 36px;
  margin-top: 40px;
  border: none;
}

.columns<PERSON><PERSON><PERSON>,
footer,
header {
  position: relative;
}

.leftColumn,
.right<PERSON>olumn,
footer,
header {
  padding: 1.25em;
}

.leftColumn {
  text-align: center;
  box-sizing: border-box;
  min-height: 100vh;
  padding: 0;
  position: relative;
  background-image: url("assets/images/others/background.png");
  background-color: #cccccc;
  background-size: 500px 500px;
  background-repeat: repeat;
}

.rg_logo {
  position: absolute;
  right: -110px;
  -ms-transform: translate(-50%, -50%);
  transform: translate(-50%, -50%);
  top: -60;
  font-size: 12px;
}

.rightColumn {
  text-align: center;
  padding: 6em 4em 0 4em;
  background-color: white;
}

.overlay {
  margin: 0;
  position: absolute;
  top: 50%;
  right: 0%;
  -ms-transform: translate(-50%, -50%);
  transform: translate(-55%, -50%);
}

.overlay img {
  box-shadow: 0 10px 16px 0 rgba(0, 0, 0, 0.2), 0 6px 20px 0 rgba(0, 0, 0, 0.19) !important;
  border-top-left-radius: 2rem;
  border-bottom-left-radius: 2rem;
  object-fit: cover;
  height: 85vh;
}

.grid-info-form {
  position: absolute;
  top: 50%;
  left: 35%;
  transform: translate(-25%, -50%);
  padding: 2rem 1rem;
  color: #fff;
  background-color: rgba(0, 0, 0, 0.2);
  border-radius: 0.5rem;
}

#Signin {
  margin-bottom: 120px;
}

@media screen and (min-width: 786px) {
  .leftColumn {
    margin-right: 20em;
  }

  .rightColumn {
    position: absolute;
    top: 0;
    right: 0;
    width: 20em;
    min-height: 100vh;
  }

  .mobile_image {
    display: none;
  }
}

@media screen and (max-width: 992px) {
  .rightColumn {
    padding: 7em 3em 0 3em;
  }
}

@media screen and (max-width: 786px) {
  .leftColumn {
    display: none;
  }

  .rightColumn {
    padding: 1em 3em 0 3em;
  }

  .rg_logo {
    position: absolute;
    right: -90;
    -ms-transform: translate(-50%, -50%);
    transform: translate(-50%, -50%);
    top: 20;
    font-size: 12px;
  }

  #Signin {
    margin-bottom: 20px;
  }
}

@media screen and (min-width: 992px) {
  .leftColumn {
    margin-right: 25em;
  }

  .rightColumn {
    position: absolute;
    top: 0;
    right: 0;
    width: 25em;
  }
}

.form__group {
  position: relative;
  padding: 15px 0 0;
  margin-top: 10px;
}

.form__field {
  font-family: inherit;
  width: 100%;
  border: 0;
  border-bottom: 1px solid #aaa;
  outline: 0;
  font-size: 14px;
  color: #555;
  padding: 7px 0;
  background: 0 0;
  transition: border-color 0.2s;
}

.form__field::placeholder {
  color: transparent;
}

.form__field:placeholder-shown ~ .form__label {
  font-size: 14px;
  cursor: text;
  top: 20px;
}

.form__field:focus ~ .form__label,
.label {
  position: absolute;
  top: 0;
  display: block;
  transition: 0.2s;
  font-size: 12px;
  color: #aaa;
}

.form__field:focus ~ .form__label {
  color: #555;
}

.form__field:focus {
  padding-bottom: 6px;
  border-bottom: 2px solid #555;
}
