button,
input,
select {
  outline: 0;
}

.btn,
button,
select {
  cursor: pointer;
}

a {
  text-decoration: none;
  color: #777;
}

a:hover {
  text-decoration: none;
  color: #d52b1e;
}

.footer-link {
  text-decoration: none;
}

.footer-link:hover {
  color: #d52b1e !important;
  text-decoration: none;
}

.footer-link p {
  color: inherit;
}

.footer-link svg {
  transition: stroke 0.3s ease;
}

.footer-link:hover svg {
  stroke: #d52b1e;
}

.small {
  font-size: 12px;
}

.mt0 {
  margin-top: 0;
}

.mt4 {
  margin-top: 40px;
}

.mb0 {
  margin-bottom: 0;
}

.actionbg,
button.btn,
.btn,
button {
  border-radius: 30px !important;
  -webkit-border-radius: 30px !important;
  -moz-border-radius: 30px !important;
  -o-border-radius: 30px !important;
  -ms-border-radius: 30px !important;
}

button.btn:hover,
.btn:hover,
button:hover {
  transition: 0.5s ease;
  -webkit-transition: 0.5s ease;
  -o-transition: 0.5s ease;
  -ms-transition: 0.5s ease;
  -moz-transition: 0.5s ease;
  background: #d66159 !important;
  color: #fff !important;
}

button.btn,
.btn {
  background: #555 !important;
  color: #fff !important;
  text-decoration: none;
  padding: 0 30px !important;
  text-align: center;
  font-size: 14px !important;
  display: inline-block;
  height: 38px !important;
  line-height: 36px !important;
  margin-top: 40px;
  border: none !important;
  width: auto !important;
}

.columnsContainer,
footer,
header {
  position: relative;
}

.leftColumn,
.rightColumn,
footer,
header {
  padding: 1.25em;
}

.leftColumn {
  text-align: center;
  box-sizing: border-box;
  min-height: 100vh;
  padding: 0;
  position: relative;
  background-image: url("assets/images/others/background.png");
  background-color: #cccccc;
  background-size: 500px 500px;
  background-repeat: repeat;
}

.rg_logo {
  position: absolute;
  right: 20px;
  top: 20px;
  -ms-transform: none;
  transform: none;
  font-size: 12px;
  z-index: 10;
}

.rightColumn {
  text-align: center;
  padding: 4em 4em 0 4em;
  background-color: white;
  box-sizing: border-box;
}

.overlay {
  margin: 0;
  position: absolute;
  top: 50%;
  right: 0%;
  -ms-transform: translate(-50%, -50%);
  transform: translate(-55%, -50%);
}

.overlay img {
  box-shadow: 0 10px 16px 0 rgba(0, 0, 0, 0.2), 0 6px 20px 0 rgba(0, 0, 0, 0.19) !important;
  border-top-left-radius: 2rem;
  border-bottom-left-radius: 2rem;
  object-fit: cover;
  height: 85vh;
}

.grid-info-form {
  position: absolute;
  top: 50%;
  left: 35%;
  transform: translate(-25%, -50%);
  padding: 2rem 1rem;
  color: #fff;
  background-color: rgba(0, 0, 0, 0.2);
  border-radius: 0.5rem;
}

#Signin {
  margin-bottom: 120px;
  background: #555 !important;
  color: #fff !important;
  border: none !important;
  border-radius: 30px !important;
  padding: 0 30px !important;
  height: 38px !important;
  line-height: 36px !important;
  font-size: 14px !important;
  cursor: pointer !important;
  display: inline-block !important;
  text-align: center !important;
  margin-top: 40px !important;
}

#Signin:hover {
  background: #d66159 !important;
  color: #fff !important;
  transition: 0.5s ease;
}

@media screen and (min-width: 786px) {
  .leftColumn {
    margin-right: 28em;
  }

  .rightColumn {
    position: absolute;
    top: 0;
    right: 0;
    width: 28em;
    min-height: 100vh;
    padding: 7em 5em 0 5em !important;
  }

  .rg_logo {
    right: -80px !important;
    top: 30px !important;
    font-size: 12px !important;
  }

  .mobile_image {
    display: none;
  }
}

@media screen and (max-width: 992px) and (min-width: 787px) {
  .rightColumn {
    padding: 5em 4em 0 4em;
  }

  .rg_logo {
    right: -60px !important;
    top: 25px !important;
    font-size: 11px !important;
  }
}

@media screen and (max-width: 786px) {
  .leftColumn {
    display: none;
  }

  .rightColumn {
    padding: 0;
    min-height: 100vh;
    width: 100%;
    position: relative;
    background-image: url("assets/images/others/background.png");
    background-color: #f5f5f5;
    background-size: cover;
    background-repeat: no-repeat;
    background-position: center;
  }

  .mobile_image {
    display: block !important;
    width: 100%;
    margin: 0;
    position: relative;
  }

  .mobile_image img {
    width: 100%;
    height: 200px;
    object-fit: cover;
    border-radius: 0;
    display: block;
  }

  /* Fallback when no banner image */
  .mobile_image:empty {
    height: 200px;
    background-image: url("assets/images/others/background.png");
    background-color: #e0e0e0;
    background-size: cover;
    background-position: center;
    background-repeat: no-repeat;
  }

  /* Form container with proper padding */
  #DivLogin {
    padding: 2em 1.5em 1em 1.5em;
    background-color: white;
    margin: 0;
    position: relative;
  }

  .rg_logo {
    position: absolute;
    right: 15px !important;
    top: 15px !important;
    -ms-transform: none !important;
    transform: none !important;
    font-size: 11px !important;
    z-index: 10;
  }

  #Signin {
    margin-bottom: 30px !important;
    margin-top: 30px !important;
    width: auto !important;
    max-width: 200px !important;
    min-width: 150px !important;
    padding: 0 25px !important;
  }

  .form__group {
    margin-top: 20px !important;
  }

  /* Footer links spacing for mobile */
  .footer-link {
    margin-bottom: 15px;
  }

  .footer-link p {
    font-size: 11px !important;
    margin-top: 5px !important;
  }

  .footer-link svg {
    width: 24px !important;
    height: 24px !important;
  }

  /* Form styling for mobile */
  .form__field {
    font-size: 16px !important; /* Prevents zoom on iOS */
    padding: 10px 0 !important;
  }

  .form__label {
    font-size: 13px !important;
  }

  /* Checkbox area for mobile */
  .form__group input[type="checkbox"] {
    transform: scale(1.2);
    margin-right: 8px !important;
  }
}

/* Extra small screens (phones in portrait) */
@media screen and (max-width: 480px) {
  #DivLogin {
    padding: 1.5em 1em 1em 1em !important;
  }

  .mobile_image img {
    height: 180px !important;
  }

  #Signin {
    max-width: 180px !important;
    min-width: 140px !important;
    font-size: 13px !important;
    height: 36px !important;
    line-height: 34px !important;
    padding: 0 20px !important;
  }

  .rg_logo {
    font-size: 10px !important;
    right: 10px !important;
    top: 10px !important;
  }

  .footer-link {
    width: 33% !important;
    padding: 0 5px !important;
  }

  .footer-link p {
    font-size: 10px !important;
  }

  .footer-link svg {
    width: 20px !important;
    height: 20px !important;
  }
}

.rg_logo {
  position: absolute;
  right: -90;
  -ms-transform: translate(-50%, -50%);
  transform: translate(-50%, -50%);
  top: 20;
  font-size: 12px;
}

#Signin {
  margin-bottom: 20px;
}

@media screen and (min-width: 992px) {
  .leftColumn {
    margin-right: 30em !important;
  }

  .rightColumn {
    position: absolute;
    top: 0;
    right: 0;
    width: 30em !important;
    padding: 5em 5em 0 5em;
  }
}

@media screen and (min-width: 1200px) {
  .leftColumn {
    margin-right: 36em;
  }

  .rightColumn {
    position: absolute;
    top: 0;
    right: 0;
    width: 36em;
    padding: 6em 6em 0 6em;
  }
}

@media screen and (min-width: 1400px) {
  .leftColumn {
    margin-right: 40em;
  }

  .rightColumn {
    position: absolute;
    top: 0;
    right: 0;
    width: 40em;
    padding: 6em 7em 0 7em;
  }
}

.form__group {
  position: relative;
  padding: 15px 0 0;
  margin-top: 10px;
}

.form__field {
  font-family: inherit;
  width: 100%;
  border: 0;
  border-bottom: 1px solid #aaa;
  outline: 0;
  font-size: 14px;
  color: #555;
  padding: 7px 0;
  background: 0 0;
  transition: border-color 0.2s;
}

.form__field::placeholder {
  color: transparent;
}

.form__field:placeholder-shown ~ .form__label {
  font-size: 14px;
  cursor: text;
  top: 20px;
}

.form__field:focus ~ .form__label,
.label {
  position: absolute;
  top: 0;
  display: block;
  transition: 0.2s;
  font-size: 12px;
  color: #aaa;
}

.form__field:focus ~ .form__label {
  color: #555;
}

.form__field:focus {
  padding-bottom: 6px;
  border-bottom: 2px solid #555;
}
