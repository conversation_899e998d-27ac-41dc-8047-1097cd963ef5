button,
input,
select {
  outline: 0;
}

.btn,
button,
select {
  cursor: pointer;
}

a {
  text-decoration: none;
  color: #777;
}

a:hover {
  text-decoration: none;
  color: #d52b1e;
}

.small {
  font-size: 12px;
}

.mt0 {
  margin-top: 0;
}

.mt4 {
  margin-top: 40px;
}

.mb0 {
  margin-bottom: 0;
}

.actionbg,
button.btn,
.btn,
button {
  border-radius: 30px !important;
  -webkit-border-radius: 30px !important;
  -moz-border-radius: 30px !important;
  -o-border-radius: 30px !important;
  -ms-border-radius: 30px !important;
}

button.btn:hover,
.btn:hover,
button:hover {
  transition: 0.5s ease;
  -webkit-transition: 0.5s ease;
  -o-transition: 0.5s ease;
  -ms-transition: 0.5s ease;
  -moz-transition: 0.5s ease;
  background: #d66159 !important;
  color: #fff !important;
}

button.btn,
.btn {
  background: #555 !important;
  color: #fff !important;
  text-decoration: none;
  padding: 0 30px !important;
  text-align: center;
  font-size: 14px !important;
  display: inline-block;
  height: 38px !important;
  line-height: 36px !important;
  margin-top: 40px;
  border: none !important;
  width: auto !important;
}

.columnsContainer,
footer,
header {
  position: relative;
}

.leftColumn,
.rightColumn,
footer,
header {
  padding: 1.25em;
}

.leftColumn {
  text-align: center;
  box-sizing: border-box;
  min-height: 100vh;
  padding: 0;
  position: relative;
  background-image: url("assets/images/others/background.png");
  background-color: #cccccc;
  background-size: 500px 500px;
  background-repeat: repeat;
}

.rg_logo {
  position: absolute;
  right: -110px;
  -ms-transform: translate(-50%, -50%);
  transform: translate(-50%, -50%);
  top: -60;
  font-size: 12px;
}

.rightColumn {
  text-align: center;
  padding: 4em 3em 0 3em;
  background-color: white;
  box-sizing: border-box;
}

.overlay {
  margin: 0;
  position: absolute;
  top: 50%;
  right: 0%;
  -ms-transform: translate(-50%, -50%);
  transform: translate(-55%, -50%);
}

.overlay img {
  box-shadow: 0 10px 16px 0 rgba(0, 0, 0, 0.2), 0 6px 20px 0 rgba(0, 0, 0, 0.19) !important;
  border-top-left-radius: 2rem;
  border-bottom-left-radius: 2rem;
  object-fit: cover;
  height: 85vh;
}

.grid-info-form {
  position: absolute;
  top: 50%;
  left: 35%;
  transform: translate(-25%, -50%);
  padding: 2rem 1rem;
  color: #fff;
  background-color: rgba(0, 0, 0, 0.2);
  border-radius: 0.5rem;
}

#Signin {
  margin-bottom: 120px;
  background: #555 !important;
  color: #fff !important;
  border: none !important;
  border-radius: 30px !important;
  padding: 0 30px !important;
  height: 38px !important;
  line-height: 36px !important;
  font-size: 14px !important;
  cursor: pointer !important;
  display: inline-block !important;
  text-align: center !important;
  margin-top: 40px !important;
}

#Signin:hover {
  background: #d66159 !important;
  color: #fff !important;
  transition: 0.5s ease;
}

@media screen and (min-width: 786px) {
  .leftColumn {
    margin-right: 28em;
  }

  .rightColumn {
    position: absolute;
    top: 0;
    right: 0;
    width: 28em;
    min-height: 100vh;
    padding: 4em 3em 0 3em;
  }

  .mobile_image {
    display: none;
  }
}

@media screen and (max-width: 992px) and (min-width: 787px) {
  .rightColumn {
    padding: 5em 3em 0 3em;
  }
}

@media screen and (max-width: 786px) {
  .leftColumn {
    display: none;
  }

  .rightColumn {
    padding: 1em 3em 0 3em;
  }

  .rg_logo {
    position: absolute;
    right: -90;
    -ms-transform: translate(-50%, -50%);
    transform: translate(-50%, -50%);
    top: 20;
    font-size: 12px;
  }

  #Signin {
    margin-bottom: 20px;
  }
}

@media screen and (min-width: 992px) {
  .leftColumn {
    margin-right: 30em !important;
  }

  .rightColumn {
    position: absolute;
    top: 0;
    right: 0;
    width: 32em !important;
    padding: 5em 4em 0 4em;
  }
}

@media screen and (min-width: 1200px) {
  .leftColumn {
    margin-right: 36em;
  }

  .rightColumn {
    position: absolute;
    top: 0;
    right: 0;
    width: 36em;
    padding: 6em 5em 0 5em;
  }
}

@media screen and (min-width: 1400px) {
  .leftColumn {
    margin-right: 40em;
  }

  .rightColumn {
    position: absolute;
    top: 0;
    right: 0;
    width: 40em;
    padding: 6em 6em 0 6em;
  }
}

.form__group {
  position: relative;
  padding: 15px 0 0;
  margin-top: 10px;
}

.form__field {
  font-family: inherit;
  width: 100%;
  border: 0;
  border-bottom: 1px solid #aaa;
  outline: 0;
  font-size: 14px;
  color: #555;
  padding: 7px 0;
  background: 0 0;
  transition: border-color 0.2s;
}

.form__field::placeholder {
  color: transparent;
}

.form__field:placeholder-shown ~ .form__label {
  font-size: 14px;
  cursor: text;
  top: 20px;
}

.form__field:focus ~ .form__label,
.label {
  position: absolute;
  top: 0;
  display: block;
  transition: 0.2s;
  font-size: 12px;
  color: #aaa;
}

.form__field:focus ~ .form__label {
  color: #555;
}

.form__field:focus {
  padding-bottom: 6px;
  border-bottom: 2px solid #555;
}
